# Python / Django
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.pot
*.mo
*.log
share/
etc/
library/
Scripts/

#projet
arcdata/
arcdata/training/
AnalysesGrilles/analyses_arc/
tempo/
training/
evaluation/
grid_analysis/
*.json

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Frontend
node_modules/
dist/
dist-ssr/
*.local
.cache/
.parcel-cache/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.pth

# Ne pas ignorer toutes les migrations, seulement celles générées automatiquement
*/migrations/*
!*/migrations/__init__.py
request.json

# Coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# API keys and secrets
# .env et .env.* sont déjà inclus plus haut
data/metadata/global_statistics.json
data/metadata/global_statistics.json
