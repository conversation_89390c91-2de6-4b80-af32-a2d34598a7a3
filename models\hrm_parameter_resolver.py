"""
HRMParameterResolver pour la résolution algorithmique.

Résolution de paramètres par recherche algorithmique améliorée.
Force brute optimisée avec analyse des caractéristiques ARC.
Pas d'apprentissage automatique - analyse factuelle des patterns.
"""

import numpy as np
import time
import itertools
from typing import Dict, List, Optional, Tuple, Any
from collections import Counter
from src.command_executor import CommandExecutor
from src.scenario_generalizer import ParameterType

class ParameterAnalyzer:
    """
    Analyseur amélioré pour extraire des informations des grilles ARC.

    Utilise les techniques d'analyse avancées développées pour l'analyse
    des caractéristiques ARC. Fournit des analyses factuelles sans IA.
    """

    @staticmethod
    def get_most_frequent_color(grid: np.ndarray, exclude_background: bool = True) -> int:
        """Retourne la couleur la plus fréquente (excluant le fond si demandé)."""
        flat_grid = grid.flatten()
        color_counts = Counter(flat_grid)

        if exclude_background and 0 in color_counts:
            del color_counts[0]

        if not color_counts:
            return 0

        return color_counts.most_common(1)[0][0]

    @staticmethod
    def get_color_distribution(grid: np.ndarray) -> Dict[int, float]:
        """Retourne la distribution des couleurs avec pourcentages."""
        flat_grid = grid.flatten()
        color_counts = Counter(flat_grid)
        total_cells = len(flat_grid)

        return {color: count / total_cells for color, count in color_counts.items()}

    @staticmethod
    def get_unique_colors_count(grid: np.ndarray, exclude_background: bool = True) -> int:
        """Retourne le nombre de couleurs uniques."""
        unique_colors = set(grid.flatten())
        if exclude_background and 0 in unique_colors:
            unique_colors.remove(0)
        return len(unique_colors)

    @staticmethod
    def get_colored_density(grid: np.ndarray) -> float:
        """Retourne la densité de cellules colorées (non-fond)."""
        total_cells = grid.size
        colored_cells = np.count_nonzero(grid)
        return colored_cells / total_cells if total_cells > 0 else 0.0
    
    @staticmethod
    def get_dominant_color_output(train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> int:
        """Retourne la couleur dominante dans les outputs des exemples train."""
        all_output_colors = []
        
        for _, output_grid in train_pairs:
            colors = output_grid.flatten()
            all_output_colors.extend(colors[colors != 0])  # Exclure le fond
        
        if not all_output_colors:
            return 1  # Couleur par défaut
        
        return Counter(all_output_colors).most_common(1)[0][0]
    
    @staticmethod
    def detect_geometric_center(grid: np.ndarray) -> Tuple[int, int]:
        """Détecte le centre géométrique de la grille."""
        h, w = grid.shape
        return (h // 2, w // 2)
    
    @staticmethod
    def detect_main_object(grid: np.ndarray) -> List[Tuple[int, int]]:
        """Détecte l'objet principal (plus grande composante connexe non-fond)."""
        # Trouver toutes les positions non-fond
        non_zero_positions = np.argwhere(grid != 0)

        if len(non_zero_positions) == 0:
            return []

        # Analyse de composantes connexes simplifiée
        # Grouper les positions adjacentes
        components = []
        visited = set()

        for pos in non_zero_positions:
            pos_tuple = (int(pos[0]), int(pos[1]))
            if pos_tuple not in visited:
                component = ParameterAnalyzer._flood_fill_component(grid, pos_tuple, visited)
                if component:
                    components.append(component)

        # Retourner la plus grande composante
        if components:
            return max(components, key=len)
        return [(int(pos[0]), int(pos[1])) for pos in non_zero_positions]

    @staticmethod
    def _flood_fill_component(grid: np.ndarray, start_pos: Tuple[int, int],
                             visited: set) -> List[Tuple[int, int]]:
        """Flood fill pour trouver une composante connexe."""
        h, w = grid.shape
        component = []
        stack = [start_pos]

        while stack:
            r, c = stack.pop()
            if (r, c) in visited or r < 0 or r >= h or c < 0 or c >= w:
                continue
            if grid[r, c] == 0:  # Fond
                continue

            visited.add((r, c))
            component.append((r, c))

            # Ajouter les voisins (4-connectivité)
            for dr, dc in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
                stack.append((r + dr, c + dc))

        return component
    
    @staticmethod
    def detect_pattern_positions(input_grid: np.ndarray, output_grid: np.ndarray) -> List[Tuple[int, int]]:
        """Détecte les positions où des changements ont eu lieu."""
        diff_mask = input_grid != output_grid
        diff_positions = np.argwhere(diff_mask)
        return [(int(pos[0]), int(pos[1])) for pos in diff_positions]
    
    @staticmethod
    def detect_symmetry_axis(grid: np.ndarray) -> str:
        """Détecte l'axe de symétrie principal."""
        h, w = grid.shape
        
        # Test symétrie horizontale
        if np.array_equal(grid, np.flip(grid, axis=1)):
            return "VERTICAL"
        
        # Test symétrie verticale
        if np.array_equal(grid, np.flip(grid, axis=0)):
            return "HORIZONTAL"
        
        return "VERTICAL"  # Par défaut
    
    @staticmethod
    def detect_rotation_angle(input_grid: np.ndarray, output_grid: np.ndarray) -> int:
        """Détecte l'angle de rotation entre input et output."""
        # Test rotations de 90, 180, 270 degrés
        for angle in [90, 180, 270]:
            rotated = np.rot90(input_grid, k=angle//90)
            if np.array_equal(rotated, output_grid):
                return angle
        
        return 0  # Pas de rotation détectée
    
    @staticmethod
    def detect_scale_factor(input_grid: np.ndarray, output_grid: np.ndarray) -> int:
        """Détecte le facteur d'échelle entre input et output."""
        input_objects = len(np.argwhere(input_grid != 0))
        output_objects = len(np.argwhere(output_grid != 0))
        
        if input_objects == 0:
            return 1
        
        scale = output_objects // input_objects
        return max(1, scale)
    
    @staticmethod
    def detect_translation_vector(input_grid: np.ndarray, output_grid: np.ndarray) -> Tuple[int, int]:
        """Détecte le vecteur de translation."""
        # Simplification : détecter le décalage du centre de masse
        input_positions = np.argwhere(input_grid != 0)
        output_positions = np.argwhere(output_grid != 0)

        if len(input_positions) == 0 or len(output_positions) == 0:
            return (0, 0)

        input_center = np.mean(input_positions, axis=0)
        output_center = np.mean(output_positions, axis=0)

        translation = output_center - input_center
        return (int(translation[0]), int(translation[1]))

    @staticmethod
    def analyze_transformation_type(input_grid: np.ndarray, output_grid: np.ndarray) -> str:
        """Analyse le type de transformation entre input et output."""
        input_shape = input_grid.shape
        output_shape = output_grid.shape

        # Analyse du changement de taille
        if input_shape != output_shape:
            size_ratio = (output_shape[0] * output_shape[1]) / (input_shape[0] * input_shape[1])
            if size_ratio > 2.0:
                return "expansion"
            elif size_ratio < 0.5:
                return "reduction"
            else:
                return "resize"

        # Même taille - analyser le contenu
        if np.array_equal(input_grid, output_grid):
            return "identity"

        # Analyser les changements de couleur
        input_colors = set(input_grid.flatten())
        output_colors = set(output_grid.flatten())

        if len(output_colors) > len(input_colors):
            return "color_addition"
        elif len(output_colors) < len(input_colors):
            return "color_reduction"

        # Analyser les changements de densité
        input_density = ParameterAnalyzer.get_colored_density(input_grid)
        output_density = ParameterAnalyzer.get_colored_density(output_grid)

        if abs(output_density - input_density) > 0.3:
            return "density_change"

        return "modification"

    @staticmethod
    def detect_tiling_pattern(input_grid: np.ndarray, output_grid: np.ndarray) -> Optional[Tuple[int, int]]:
        """Détecte si l'output est un tiling de l'input."""
        input_h, input_w = input_grid.shape
        output_h, output_w = output_grid.shape

        # Vérifier si les dimensions sont des multiples
        if output_h % input_h == 0 and output_w % input_w == 0:
            tile_h = output_h // input_h
            tile_w = output_w // input_w

            # Vérifier si l'output est effectivement un tiling
            for i in range(tile_h):
                for j in range(tile_w):
                    start_r = i * input_h
                    end_r = (i + 1) * input_h
                    start_c = j * input_w
                    end_c = (j + 1) * input_w

                    tile = output_grid[start_r:end_r, start_c:end_c]
                    if not np.array_equal(tile, input_grid):
                        return None

            return (tile_h, tile_w)

        return None

class ResolvedParameter:
    """Paramètre résolu avec sa valeur concrète."""
    param_name: str
    param_type: ParameterType
    resolved_value: Any
    confidence: float = 1.0

class HRMParameterResolver:
    """
    Résolution de paramètres par recherche algorithmique améliorée.
    Force brute optimisée avec analyse des caractéristiques ARC.
    Pas d'apprentissage automatique - analyse factuelle des patterns.
    """

    def __init__(self, timeout_seconds: int = 10):
        """Initialise le résolveur avec timeout et exécuteur de commandes."""
        self.timeout_seconds = timeout_seconds
        self.command_executor = CommandExecutor()
        self.analyzer = ParameterAnalyzer()
        
    def resolve_parameters(self, template: str, test_input: np.ndarray,
                          train_examples: List) -> Optional[str]:
        """
        Algorithme de résolution amélioré :
        1. Analyser les caractéristiques des exemples d'entraînement
        2. Énumérer valeurs possibles basées sur l'analyse
        3. Prioriser les combinaisons selon les patterns détectés
        4. Tester combinaisons sur train examples
        5. Sélectionner meilleure combinaison (score)
        """
        start_time = time.time()

        # 1. Analyser les caractéristiques des exemples
        characteristics = self._analyze_training_characteristics(train_examples)

        # 2. Énumérer valeurs possibles basées sur l'analyse
        parameter_values = self._enumerate_parameter_values_enhanced(template, train_examples, characteristics)

        if not parameter_values:
            return None
        
        # 3. Prioriser les combinaisons selon les patterns
        prioritized_combinations = self._prioritize_combinations(parameter_values, characteristics)

        # 4. Tester combinaisons sur train examples
        best_scenario = None
        best_score = 0.0

        param_names = list(parameter_values.keys())
        combinations_tested = 0

        for combination in prioritized_combinations:
            # Vérifier le timeout
            if time.time() - start_time > self.timeout_seconds:
                print(f"Timeout dépassé ({self.timeout_seconds}s) après {combinations_tested} combinaisons")
                break
            
            # Créer le scénario avec cette combinaison
            scenario = self._instantiate_template(template, param_names, combination)

            # 5. Tester sur les exemples train
            score = self.validate_solution(scenario, train_examples)
            combinations_tested += 1
            
            if score > best_score:
                best_score = score
                best_scenario = scenario
                
                # Si score parfait, arrêter la recherche (early stopping)
                if score >= 1.0:
                    print(f"Solution parfaite trouvée après {combinations_tested} combinaisons")
                    break
        
        print(f"Recherche terminée: {combinations_tested} combinaisons testées, meilleur score: {best_score:.2f}")
        
        # Pour la démonstration, retourner le meilleur scénario même avec score faible
        # Dans un vrai système, on utiliserait un seuil plus strict
        if best_scenario is not None:
            return best_scenario
        
        # Fallback: retourner le template avec des valeurs par défaut
        if parameter_values:
            param_names = list(parameter_values.keys())
            default_combination = tuple(parameter_values[name][0] if parameter_values[name] else "1"
                                      for name in param_names)
            fallback_scenario = self._instantiate_template(template, param_names, default_combination)
            print(f"Utilisation du scénario de fallback: {fallback_scenario}")
            return fallback_scenario
        
        return None
    
    def validate_solution(self, scenario: str, train_examples: List) -> float:
        """
        Validation par exécution :
        - Exécuter scenario sur chaque train input
        - Comparer avec train output attendu
        - Calculer score de correspondance (0.0 à 1.0)
        """
        if not train_examples:
            return 0.0
        
        successful_executions = 0
        total_examples = len(train_examples)
        
        for input_grid, expected_output in train_examples:
            try:
                # Exécuter le scénario sur cet exemple
                result = self._execute_scenario_on_grid(scenario, input_grid)
                
                if result is not None and np.array_equal(result, expected_output):
                    successful_executions += 1
                    
            except Exception:
                continue  # Échec d'exécution pour cet exemple
        
        return successful_executions / total_examples

    def _analyze_training_characteristics(self, train_examples: List) -> Dict[str, Any]:
        """Analyse les caractéristiques des exemples d'entraînement."""
        if not train_examples:
            return {}

        characteristics = {
            'transformation_types': [],
            'size_changes': [],
            'color_changes': [],
            'density_changes': [],
            'tiling_patterns': [],
            'dominant_colors': [],
            'common_shapes': [],
            'translation_vectors': []
        }

        for input_grid, output_grid in train_examples:
            # Analyser le type de transformation
            transform_type = self.analyzer.analyze_transformation_type(input_grid, output_grid)
            characteristics['transformation_types'].append(transform_type)

            # Analyser les changements de taille
            input_size = input_grid.shape
            output_size = output_grid.shape
            size_ratio = (output_size[0] * output_size[1]) / (input_size[0] * input_size[1])
            characteristics['size_changes'].append(size_ratio)

            # Analyser les changements de couleur
            input_colors = self.analyzer.get_unique_colors_count(input_grid)
            output_colors = self.analyzer.get_unique_colors_count(output_grid)
            characteristics['color_changes'].append(output_colors - input_colors)

            # Analyser les changements de densité
            input_density = self.analyzer.get_colored_density(input_grid)
            output_density = self.analyzer.get_colored_density(output_grid)
            characteristics['density_changes'].append(output_density - input_density)

            # Détecter les patterns de tiling
            tiling = self.analyzer.detect_tiling_pattern(input_grid, output_grid)
            characteristics['tiling_patterns'].append(tiling)

            # Couleurs dominantes
            dominant_color = self.analyzer.get_most_frequent_color(output_grid)
            characteristics['dominant_colors'].append(dominant_color)

            # Vecteurs de translation
            translation = self.analyzer.detect_translation_vector(input_grid, output_grid)
            characteristics['translation_vectors'].append(translation)

        # Calculer les patterns dominants
        characteristics['dominant_transform_type'] = Counter(characteristics['transformation_types']).most_common(1)[0][0] if characteristics['transformation_types'] else 'modification'
        characteristics['avg_size_change'] = np.mean(characteristics['size_changes']) if characteristics['size_changes'] else 1.0
        characteristics['avg_color_change'] = np.mean(characteristics['color_changes']) if characteristics['color_changes'] else 0
        characteristics['avg_density_change'] = np.mean(characteristics['density_changes']) if characteristics['density_changes'] else 0

        return characteristics
    
    def _enumerate_parameter_values_enhanced(self, template: str, train_examples: List,
                                           characteristics: Dict[str, Any]) -> Dict[str, List]:
        """Énumération améliorée des valeurs possibles basée sur l'analyse des caractéristiques."""
        import re

        # Extraire les variables du template
        variables = re.findall(r'\{([a-zA-Z_][a-zA-Z0-9_]*)\}', template)
        parameter_values = {}

        for var in variables:
            if 'color' in var.lower():
                parameter_values[var] = self._get_enhanced_possible_colors(train_examples, characteristics)
            elif 'angle' in var.lower():
                parameter_values[var] = self._get_enhanced_possible_angles(characteristics)
            elif 'center' in var.lower():
                parameter_values[var] = self._get_enhanced_possible_centers(train_examples, characteristics)
            elif 'size' in var.lower() or 'width' in var.lower() or 'height' in var.lower():
                parameter_values[var] = self._get_enhanced_possible_sizes(train_examples, characteristics)
            elif 'region' in var.lower() or 'area' in var.lower() or 'positions' in var.lower():
                parameter_values[var] = self._get_enhanced_possible_regions(train_examples, characteristics)
            elif 'axis' in var.lower():
                parameter_values[var] = self._get_enhanced_possible_axes(characteristics)
            elif 'factor' in var.lower():
                parameter_values[var] = self._get_enhanced_possible_factors(characteristics)
            elif 'method' in var.lower():
                parameter_values[var] = ["nearest"]
            else:
                parameter_values[var] = ["1", "2", "3"]  # Valeurs par défaut

        return parameter_values

    def _prioritize_combinations(self, parameter_values: Dict[str, List],
                               characteristics: Dict[str, Any]) -> List[Tuple]:
        """Priorise les combinaisons de paramètres selon les patterns détectés."""
        param_names = list(parameter_values.keys())
        all_combinations = list(itertools.product(*[parameter_values[name] for name in param_names]))

        # Scorer chaque combinaison selon les caractéristiques
        scored_combinations = []
        for combination in all_combinations:
            score = self._score_combination(param_names, combination, characteristics)
            scored_combinations.append((score, combination))

        # Trier par score décroissant
        scored_combinations.sort(key=lambda x: x[0], reverse=True)

        # Retourner les combinaisons triées (sans les scores)
        return [combo for score, combo in scored_combinations]

    def _score_combination(self, param_names: List[str], combination: Tuple,
                          characteristics: Dict[str, Any]) -> float:
        """Score une combinaison de paramètres selon les caractéristiques."""
        score = 0.0

        for i, param_name in enumerate(param_names):
            value = combination[i]

            # Scorer selon le type de paramètre et les caractéristiques
            if 'color' in param_name.lower():
                # Privilégier les couleurs dominantes
                if int(value) in characteristics.get('dominant_colors', []):
                    score += 2.0
                else:
                    score += 1.0

            elif 'factor' in param_name.lower():
                # Privilégier les facteurs cohérents avec les changements de taille
                avg_size_change = characteristics.get('avg_size_change', 1.0)
                factor_value = int(value)
                if abs(factor_value - avg_size_change) < 0.5:
                    score += 2.0
                else:
                    score += 1.0

            elif 'axis' in param_name.lower():
                # Privilégier selon le type de transformation dominant
                transform_type = characteristics.get('dominant_transform_type', '')
                if 'symmetry' in transform_type or 'flip' in transform_type:
                    score += 2.0
                else:
                    score += 1.0

            else:
                score += 1.0  # Score de base

        return score

    def _enumerate_parameter_values(self, template: str, train_examples: List) -> Dict[str, List]:
        """Énumération des valeurs possibles pour chaque paramètre"""
        import re
        
        # Extraire les variables du template (éviter les accolades de structure MOTIF)
        # Chercher seulement les variables avec des noms valides (lettres, chiffres, underscore)
        variables = re.findall(r'\{([a-zA-Z_][a-zA-Z0-9_]*)\}', template)
        parameter_values = {}
        
        for var in variables:
            if 'color' in var.lower():
                parameter_values[var] = self._get_possible_colors(train_examples)
            elif 'angle' in var.lower():
                # Conformité grammaire : ROTATE ("LEFT" | "RIGHT")
                parameter_values[var] = ["LEFT", "RIGHT"]
            elif 'center' in var.lower():
                parameter_values[var] = self._get_possible_centers(train_examples)
            elif 'size' in var.lower() or 'width' in var.lower() or 'height' in var.lower():
                parameter_values[var] = self._get_possible_sizes(train_examples)
            elif 'region' in var.lower() or 'area' in var.lower() or 'positions' in var.lower():
                parameter_values[var] = self._get_possible_regions(train_examples)
            elif 'axis' in var.lower():
                # Conformité grammaire : FLIP ("HORIZONTAL" | "VERTICAL")
                parameter_values[var] = ["HORIZONTAL", "VERTICAL"]
            elif 'factor' in var.lower():
                parameter_values[var] = ["2", "3", "4"]  # STRING pour conformité
            elif 'method' in var.lower():
                parameter_values[var] = ["nearest"]
            else:
                parameter_values[var] = ["1", "2", "3"]  # Valeurs par défaut
        
        return parameter_values

    def _instantiate_template(self, template: str, param_names: List[str], combination: tuple) -> str:
        """
        Instancie un template avec des valeurs de paramètres.
        Assure la conformité avec la grammaire ARCgrammarv9.lark.
        """
        scenario = template

        # Debug: afficher le template original
        # print(f"DEBUG: Template original: {template}")
        # print(f"DEBUG: Paramètres: {param_names}")
        # print(f"DEBUG: Valeurs: {combination}")

        for i, param_name in enumerate(param_names):
            value = str(combination[i])
            placeholder = f"{{{param_name}}}"

            # Formatage spécial selon le type de paramètre
            if 'width' in param_name.lower() or 'height' in param_name.lower():
                # Pour les dimensions dans les rectangles, s'assurer que c'est un entier
                if 'x' in value:
                    # Si c'est un GRID_SIZE, extraire la première dimension
                    value = value.split('x')[0]
                scenario = scenario.replace(placeholder, value)
            elif 'size' in param_name.lower():
                # Pour GRID_SIZE, s'assurer du format INTxINT
                if 'x' not in value:
                    scenario = scenario.replace(placeholder, f"{value}x{value}")
                else:
                    scenario = scenario.replace(placeholder, value)
            else:
                # Autres paramètres : utilisation directe
                scenario = scenario.replace(placeholder, value)

            # Debug: afficher après chaque substitution
            # print(f"DEBUG: Après substitution {placeholder} -> {value}: {scenario}")

        return scenario

    def _test_parameter_combination(self, scenario: str, train_examples: List) -> bool:
        """Test d'une combinaison de paramètres sur tous les exemples"""
        return self.validate_solution(scenario, train_examples) >= 0.5
    
    def _get_possible_colors(self, train_examples: List) -> List[str]:
        """Extrait les couleurs possibles des exemples"""
        colors = set()
        for input_grid, output_grid in train_examples:
            colors.update(np.unique(input_grid))
            colors.update(np.unique(output_grid))
        # Exclure le fond (0) et limiter à 5 couleurs pour éviter l'explosion combinatoire
        return [str(c) for c in sorted(colors) if c != 0][:5]
    
    def _get_possible_centers(self, train_examples: List) -> List[str]:
        """Extrait les centres possibles - Conformité grammaire : cell format [INT,INT]"""
        centers = []
        for input_grid, _ in train_examples:
            h, w = input_grid.shape
            # Conformité grammaire : cell: "[" INT "," INT "]"
            centers.append(f"[{h//2},{w//2}]")
        return list(set(centers))
    
    def _get_possible_sizes(self, train_examples: List) -> List[str]:
        """Extrait les tailles possibles - Conformité grammaire : GRID_SIZE format INTxINT"""
        sizes = []
        dimensions = []
        for input_grid, output_grid in train_examples:
            h1, w1 = input_grid.shape
            h2, w2 = output_grid.shape
            # Pour GRID_SIZE (RESIZE) : format INTxINT
            sizes.extend([f"{w1}x{h1}", f"{w2}x{h2}"])
            # Pour width/height dans les rectangles : dimensions individuelles (0-based)
            dimensions.extend([str(w1-1), str(h1-1), str(w2-1), str(h2-1)])

        # Retourner les tailles pour GRID_SIZE et les dimensions pour rectangles
        all_values = list(set(sizes + dimensions))
        return all_values[:8]  # Limiter pour éviter l'explosion combinatoire
    
    def _get_possible_regions(self, train_examples: List) -> List[str]:
        """Extrait les régions possibles - Conformité grammaire : rectangle format [INT,INT INT,INT]"""
        regions = []
        for input_grid, _ in train_examples:
            h, w = input_grid.shape
            # Conformité grammaire : rectangle: "[" INT "," INT " " INT "," INT "]"
            regions.extend([
                f"[0,0 {w-1},{h-1}]",  # Grille complète (indices 0-based)
                f"[0,0 {w//2},{h//2}]",  # Quart supérieur gauche
                f"[{w//2},{h//2} {w-1},{h-1}]",  # Quart inférieur droit
                f"[0,0]",  # Cellule origine (pour PASTE)
                f"[{w//2},{h//2}]"  # Cellule centre
            ])
        # Limiter pour éviter l'explosion combinatoire
        return list(set(regions))[:5]
    
    def _execute_scenario_on_grid(self, scenario: str, input_grid: np.ndarray) -> Optional[np.ndarray]:
        """Exécute un scénario sur une grille"""
        try:
            h, w = input_grid.shape
            # Conformité grammaire : INIT GRID_SIZE où GRID_SIZE: INT "x" INT
            commands = [f"INIT {w}x{h}", scenario]
            
            # Créer un nouvel exécuteur pour éviter les interférences
            executor = CommandExecutor()
            executor.grid = input_grid.copy()
            executor.width = w
            executor.height = h
            
            result_dict = executor.execute_commands(commands)
            
            if result_dict["success"] and result_dict["grid"] is not None:
                return np.array(result_dict["grid"])
            else:
                return None
                
        except Exception:
            return None
    
    def get_resolver_stats(self) -> Dict[str, Any]:
        """Retourne des statistiques sur le résolveur (pour debugging)"""
        return {
            'method': 'algorithmic_search',
            'timeout_seconds': self.timeout_seconds,
            'search_strategy': 'brute_force_optimized',
            'early_stopping': True,
            'note': 'Recherche algorithmique par force brute, pas d\'apprentissage automatique'
        }